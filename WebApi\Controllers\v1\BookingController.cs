﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Features.Bookings.Command;
using Application.Features.Bookings.CreateBooking;
using Application.Features.Bookings.GetBookingById;
using Application.Features.Bookings.GetFareDetails;
using Application.Features.Bookings.GetUserBookings;
using Application.Interfaces.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class BookingController : BaseApiController
    {
        private readonly IBookingRepositoryAsync bookingRepositoryAsync;

        public BookingController(IBookingRepositoryAsync bookingRepositoryAsync)
        {
            this.bookingRepositoryAsync = bookingRepositoryAsync;
        }
      
        // GET api/<controller>/5
        [HttpGet("{bookingId}")]
        public async Task<IActionResult> Get(string bookingId)
        {
            return Ok(await Mediator.Send(new GetBookingByIdQuery { BookingId = bookingId }));
            
        }

        [HttpGet("PaymentOption")]
        public async Task<IActionResult> Get(decimal bookingId,int PaymentOption)
        {
  
            return Ok();

        }

        // POST api/<controller>
        [HttpPost("NewBooking")]
        [Authorize]
        public async Task<IActionResult> Post(BookingCommand command)
        {
            var isAuthenticated = User.Identity.IsAuthenticated;
            var userName = User.Identity.Name;
            var userId = User.FindFirst("uid")?.Value; // Get the user ID from claims

            Console.WriteLine($"Controller - Is Authenticated: {isAuthenticated}");
            Console.WriteLine($"Controller - User Name: {userName}");
            Console.WriteLine($"Controller - User ID: {userId}");

            // Set the BookingCreatedBy property with the authenticated user's ID
            if (!string.IsNullOrEmpty(userId))
            {
                command.BookingCreatedBy = userId;
            }

            // You can also set other user-related properties if needed
            if (string.IsNullOrEmpty(command.TravelerName) && !string.IsNullOrEmpty(userName))
            {
                command.TravelerName = userName;
            }

            try
            {
                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                // Log the detailed exception
                Console.WriteLine($"Exception in Mediator.Send: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner exception type: {ex.InnerException.GetType().Name}");
                }

                // Return a more detailed error response for debugging
                return StatusCode(500, new
                {
                    Succeeded = false,
                    Message = "Error processing booking command",
                    Error = ex.Message,
                    InnerError = ex.InnerException?.Message,
                    ExceptionType = ex.GetType().Name
                });
            }
        }

        // POST api/<controller>
        [HttpGet("GetFareDetails")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string pickUpAddressLongLat, [FromQuery] string dropOffAddressLongLat, [FromQuery] string tripType)
        {
            return Ok(await Mediator.Send(new GetFareByParamQuery {PickUpAddressLongLat=pickUpAddressLongLat,DropOffAddressLongLat=dropOffAddressLongLat,TripType=tripType}));
        }

        // POST api/<controller>
        [HttpGet("GetSelectedtRouteCategoryFareDetails")]
        // [Authorize]
        public async Task<IActionResult> Get([FromQuery] string pickUpAddressLongLat, [FromQuery] string dropOffAddressLongLat, [FromQuery] string tripType, [FromQuery] string categoryName)
        {
            return Ok(await Mediator.Send(new GetSelectedRouteCategoryFareDetails { PickUpAddressLongLat = pickUpAddressLongLat, DropOffAddressLongLat = dropOffAddressLongLat, TripType = tripType ,CarCagetgory=categoryName}));
        }


        // POST api/<controller>
        [HttpPost("PaymentStatusUpdate")]
        // [Authorize]
        public async Task<IActionResult> Post([FromBody] CreateBookingPaymentCommand createBookingPaymentCommand)
        {
            return Ok(await Mediator.Send(createBookingPaymentCommand));
        }

        // GET api/<controller>/user-bookings
        [HttpGet("user-bookings")]
        [Authorize]
        public async Task<IActionResult> GetUserBookings([FromQuery] string cursor = null, [FromQuery] int pageSize = 10)
        {
            var userId = User.FindFirst("uid")?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { Message = "User ID not found in token" });
            }

            // Limit page size to maximum of 10
            if (pageSize > 10)
            {
                pageSize = 10;
            }

            var query = new GetUserBookingsQuery
            {
                UserId = userId,
                Cursor = cursor,
                PageSize = pageSize
            };

            try
            {
                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Succeeded = false,
                    Message = "Error retrieving user bookings",
                    Error = ex.Message
                });
            }
        }


    }
}