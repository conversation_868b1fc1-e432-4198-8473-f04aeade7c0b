-- =============================================
-- CabYaari Database Schema Changes for Partial Payment Support
-- =============================================

-- 1. Add new columns to RLT_BOOKING table
USE [CabYaari]
GO

ALTER TABLE [dbo].[RLT_BOOKING]
ADD 
    PaymentType NVARCHAR(10) NULL,
    PartialPaymentAmount DECIMAL(18,2) NULL,
    RemainingAmountForDriver DECIMAL(18,2) NULL
GO

-- 2. Add new columns to RLT_BOOKING_Histrory table (if exists)
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'RLT_BOOKING_Histrory')
BEGIN
    ALTER TABLE [dbo].[RLT_BOOKING_Histrory]
    ADD 
        PaymentType NVARCHAR(10) NULL,
        PartialPaymentAmount DECIMAL(18,2) NULL,
        RemainingAmountForDriver DECIMAL(18,2) NULL
END
GO

-- 3. Create new stored procedure usp_Booking_Create_V2
CREATE PROCEDURE [dbo].[usp_Booking_Create_V2]   
    @Booking_Id nvarchar(30) NULL,  
    @PickUpCity nvarchar(30) NULL,  
    @DropOffCity nvarchar(30) NULL,  
    @TripType nvarchar(30) NULL,  
    @CarCategory nvarchar(30) NULL,  
    @Duration nvarchar(30) NULL,  
    @Distance decimal NULL,  
    @BasicFare decimal NULL,  
    @DriverCharge decimal,  
    @GST decimal NULL,  
    @Fare decimal NULL,  
    @GSTFare decimal NULL,  
    @CouponCode nvarchar(50) NULL,  
    @CouponDiscount decimal NULL,  
    @PickUpAddress nvarchar(200) NULL,  
    @DropOffAddress nvarchar(200) NULL,  
    @PickUpDate date NULL,  
    @PickUpTime nvarchar(10) NULL,  
    @TravelerName nvarchar(200) NULL,  
    @PhoneNumber nvarchar(30) NULL,  
    @MailId nvarchar(30) NULL,  
    @PaymentMode int NULL,  
    @BookingCreatedBy nvarchar(30) NULL,  
    @RazorpayPaymentID nvarchar(30) NULL,  
    @RazorpayOrderID nvarchar(30) NULL,  
    @RazorpaySignature nvarchar(30) NULL,  
    @RazorpayStatus nvarchar(30) NULL,  
    @PickUpAddressLongLat nvarchar(100) NULL,  
    @PickUpAddressLongitude nvarchar(100) NULL,  
    @CashAmountToPayDriver decimal,  
    @PaymentOption int NULL,  
    @TollCharge decimal NULL,
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    @result nvarchar(50) NULL output  
WITH RECOMPILE   
AS  
BEGIN  
    SET NOCOUNT ON;  
    SET XACT_ABORT ON;  
    
    DECLARE @errorMessage nvarchar(max),  
           @ScriptRanOn datetime = GETDATE(),  
           @Booking_Date datetime = GETDATE(),  
           @FromCityID int = 0,  
           @TripTypeID int = 0,  
           @CategoryID int = 0,  
           @ToCityID int = 0,
           @UserID int = 0
    
    SET @FromCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @PickUpCity AND Is_Active = 1);  
    SET @ToCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @DropOffCity AND Is_Active = 1); 
    SET @TripTypeID = (SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type = @TripType AND Is_Active = 1);  
    SET @CategoryID = (SELECT TOP 1 PKID FROM RLT_CAR_CATEGORY WHERE Car_Category_Abbr = @CarCategory AND Is_Active = 1);  
    SET @UserID = (SELECT TOP 1 ID FROM [Identity].[User] WHERE UserName = @BookingCreatedBy); 
    
    -- Set default payment type if not provided
    IF @PaymentType IS NULL OR @PaymentType = ''
        SET @PaymentType = 'FULL'
    
    BEGIN TRANSACTION  
        INSERT INTO [dbo].[RLT_BOOKING](
            Booking_Id,  
            City_From_Id,   
            City_To_Id,  
            Trip_Type_Id,  
            Car_Category_Id,  
            Duration,  
            Distance,  
            Basic_Fare,  
            Driver_Charge,  
            GST,  
            Fare,  
            GST_Fare,  
            Coupon_Code,  
            Coupon_Discount,  
            Booking_Date,  
            PickUp_Address,  
            DropOff_Address,  
            PickUp_Date,  
            PickUp_Time,  
            [Name],  
            Mobile_No1,  
            Mail_Id,  
            Mode_Of_Payment_Id,  
            Booking_Status_Id,  
            Created_By,  
            razorpay_payment_id,  
            razorpay_order_id,  
            razorpay_signature,  
            razorpay_status,  
            PickUpAddressLatitude,  
            PickUpAddressLongitude,  
            CashAmountToPayDriver,  
            PaymentOption,  
            TollCharge,
            PaymentType,
            PartialPaymentAmount,
            RemainingAmountForDriver
        ) VALUES (  
            @Booking_Id,  
            @FromCityID,  
            @ToCityID,  
            @TripTypeID,  
            @CategoryID,  
            @Duration,  
            @Distance,  
            @BasicFare,  
            @DriverCharge,  
            @GST,  
            @Fare,  
            @GSTFare,  
            @CouponCode,  
            @CouponDiscount,  
            GETDATE(),  
            @PickUpAddress,  
            @DropOffAddress,  
            @PickUpDate,  
            @PickUpTime,  
            @TravelerName,  
            @PhoneNumber,  
            @MailId,  
            @PaymentMode,  
            1, /*New Booking Request*/  
            @UserID,  
            @RazorpayPaymentID,  
            @RazorpayOrderID,  
            @RazorpaySignature,  
            @RazorpayStatus,  
            @PickUpAddressLongLat,  
            @PickUpAddressLongitude,  
            @CashAmountToPayDriver,  
            @PaymentOption,  
            @TollCharge,
            @PaymentType,
            @PartialPaymentAmount,
            @RemainingAmountForDriver
        )  
           
        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Create_V2 => final--commiting sql transaction'  
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT  
    COMMIT TRANSACTION   

    SET @result = SCOPE_IDENTITY()
END
GO

-- 4. Create new stored procedure usp_Booking_Update_V2
CREATE PROCEDURE [dbo].[usp_Booking_Update_V2]
    @BookingId nvarchar(30),
    @RazorpayPaymentId nvarchar(30),
    @RazorpayOrderid nvarchar(30),
    @RazorpaySignature nvarchar(30),
    @RazorpayStatus nvarchar(30),
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @ScriptRanOn datetime = GETDATE(),
           @TransactionId nvarchar(30),
           @BookingStatusId int,
           @BookingPKID int

    -- Get the appropriate booking status ID based on payment status
    SET @BookingStatusId = CASE
        WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Confirmed/Paid booking
        WHEN @RazorpayStatus IN ('Failed') THEN 6  -- Cancelled booking (from BookingStatus enum)
        ELSE 1  -- Pending booking
    END

    BEGIN TRANSACTION
        -- Update the booking record with payment information
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @RazorpayPaymentId,
            razorpay_signature = @RazorpaySignature,
            razorpay_status = @RazorpayStatus,
            Booking_Status_Id = @BookingStatusId,
            PaymentType = ISNULL(@PaymentType, PaymentType),
            PartialPaymentAmount = ISNULL(@PartialPaymentAmount, PartialPaymentAmount),
            RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, RemainingAmountForDriver),
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId

        -- Get the booking information for the response
        SELECT
            @TransactionId = razorpay_payment_id,
            @BookingPKID = PKID
        FROM
            RLT_BOOKING
        WHERE
            Booking_Id = @BookingId

        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => committing sql transaction'
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT
    COMMIT TRANSACTION

    -- Return the booking and transaction information
    SELECT
        @BookingId AS BookingId,
        @TransactionId AS TransactionId,
        @RazorpayStatus AS PaymentStatus,
        @PaymentType AS PaymentType,
        @PartialPaymentAmount AS PartialPaymentAmount,
        @RemainingAmountForDriver AS RemainingAmountForDriver
END
GO

-- 5. Create indexes for better performance on new columns
CREATE NONCLUSTERED INDEX [IX_RLT_BOOKING_PaymentType]
ON [dbo].[RLT_BOOKING] ([PaymentType])
WHERE [PaymentType] IS NOT NULL
GO

-- 6. Add constraints to ensure data integrity
ALTER TABLE [dbo].[RLT_BOOKING]
ADD CONSTRAINT [CK_RLT_BOOKING_PaymentType]
CHECK ([PaymentType] IN ('PARTIAL', 'FULL') OR [PaymentType] IS NULL)
GO

ALTER TABLE [dbo].[RLT_BOOKING]
ADD CONSTRAINT [CK_RLT_BOOKING_PartialPaymentAmount]
CHECK ([PartialPaymentAmount] >= 0 OR [PartialPaymentAmount] IS NULL)
GO

ALTER TABLE [dbo].[RLT_BOOKING]
ADD CONSTRAINT [CK_RLT_BOOKING_RemainingAmountForDriver]
CHECK ([RemainingAmountForDriver] >= 0 OR [RemainingAmountForDriver] IS NULL)
GO

-- 7. Update existing records to have default payment type
UPDATE [dbo].[RLT_BOOKING]
SET PaymentType = 'FULL',
    RemainingAmountForDriver = 0
WHERE PaymentType IS NULL
GO

PRINT 'Database schema changes completed successfully!'
PRINT 'New stored procedures created: usp_Booking_Create_V2, usp_Booking_Update_V2'
PRINT 'New columns added: PaymentType, PartialPaymentAmount, RemainingAmountForDriver'
