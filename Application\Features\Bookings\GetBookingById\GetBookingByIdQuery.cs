﻿using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using Dapper;
using Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetBookingById
{
    public class GetBookingByIdQuery : IRequest<Response<RLT_BOOKING>>
    {
        public string BookingId { get; set; }
        public class GetBookingByIdQueryHandler : IRequestHandler<GetBookingByIdQuery, Response<RLT_BOOKING>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            public GetBookingByIdQueryHandler(IBookingRepositoryAsync bookingRepositoryAsync)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
            }
            public async Task<Response<RLT_BOOKING>> Handle(GetBookingByIdQuery query, CancellationToken cancellationToken)
            {
               
                var booking= await _bookingRepositoryAsync.GetByUniqueIdAsync(query.BookingId);
                 if (booking == null) throw new ApiException($"Booking Not Found.");
                return new Response<RLT_BOOKING>(booking);
            }
        }
    }
}
