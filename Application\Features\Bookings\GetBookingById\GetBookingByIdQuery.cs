﻿using Application.DTOs.Booking;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using Dapper;
using Domain.Entities;
using MediatR;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetBookingById
{
    public class GetBookingByIdQuery : IRequest<Response<BookingDetailsResponse>>
    {
        public string BookingId { get; set; }
        public string RequestingUserId { get; set; } // For ownership validation
        public class GetBookingByIdQueryHandler : IRequestHandler<GetBookingByIdQuery, Response<BookingDetailsResponse>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IMapper _mapper;

            public GetBookingByIdQueryHandler(IBookingRepositoryAsync bookingRepositoryAsync, IMapper mapper)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _mapper = mapper;
            }
            public async Task<Response<BookingDetailsResponse>> Handle(GetBookingByIdQuery query, CancellationToken cancellationToken)
            {
                var booking = await _bookingRepositoryAsync.GetByUniqueIdAsync(query.BookingId);

                if (booking == null)
                    throw new ApiException("Booking Not Found.");

                // ✅ Ownership validation - ensure user can only access their own bookings
                if (!string.IsNullOrEmpty(query.RequestingUserId) &&
                    booking.BookingCreatedBy != query.RequestingUserId)
                {
                    throw new Exceptions.UnauthorizedAccessException("You don't have access to this booking");
                }

                // ✅ Map to secure DTO (excludes sensitive data)
                var response = _mapper.Map<BookingDetailsResponse>(booking);

                // ✅ Map payment status from internal to user-friendly
                response.PaymentStatus = MapPaymentStatus(booking.RazorpayStatus);

                return new Response<BookingDetailsResponse>(response);
            }

            private string MapPaymentStatus(string internalStatus)
            {
                return internalStatus?.ToLower() switch
                {
                    "paid" => "Completed",
                    "pending" => "Pending",
                    "failed" => "Failed",
                    _ => "Unknown"
                };
            }
        }
    }
}
