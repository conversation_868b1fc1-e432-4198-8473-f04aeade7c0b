using System;
using System.Text.Json.Serialization;

namespace Application.DTOs.Payment
{
    public class PhonePePaymentRequest
    {
        public string MerchantTransactionId { get; set; }
        public decimal Amount { get; set; }
        public string BookingId { get; set; }
        public string OrderId { get; set; }
        public string PaymentId { get; set; }
        public string Signature { get; set; }
    }

    public class PhonePePaymentResponse
    {
        public string TokenUrl { get; set; }
        public string MerchantTransactionId { get; set; }
        public string OrderId { get; set; }
    }

    public class PhonePeVerifyRequest
    {
        public string MerchantTransactionId { get; set; }
        public string OrderId { get; set; }
        public string PaymentId { get; set; }
        public string Signature { get; set; }
    }

    public class PhonePeVerifyResponse
    {
        public bool Succeeded { get; set; }
        public string Message { get; set; }
        public PhonePeVerifyData Data { get; set; }
    }

    public class PhonePeVerifyData
    {
        public string PaymentStatus { get; set; }
        public string TransactionId { get; set; }
        public string OrderId { get; set; }
        public string BookingId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentId { get; set; }
        public string PaymentType { get; set; } // "PARTIAL" or "FULL"
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
    }

    public class PhonePePaymentStatus
    {
        public const string SUCCESS = "SUCCESS";
        public const string FAILED = "FAILED";
        public const string PENDING = "PENDING";
    }

    public class PaymentType
    {
        public const string PARTIAL = "PARTIAL";
        public const string FULL = "FULL";
    }
} 