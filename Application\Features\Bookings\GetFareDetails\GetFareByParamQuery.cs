﻿using Application.DTOs.Booking;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using Dapper;
using Domain.Settings;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetFareDetails
{
    public  class GetFareByParamQuery : IRequest<Response<List<FareDistanceCalculateResponse>>>
    {

        public string PickUpAddressLongLat { get; set; }

        public string DropOffAddressLongLat { get; set; }

        public string TripType { get; set; }



        public class GetBookingByIdQueryHandler : IRequestHandler<GetFareByParamQuery, Response<List<FareDistanceCalculateResponse>>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IConfiguration _configuration;
            public MapSettings _mapSettings { get; }
            public GetBookingByIdQueryHandler(IBookingRepositoryAsync bookingRepositoryAsync, IConfiguration configuration, IOptions<MapSettings> mapSettings)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _configuration = configuration;
                _mapSettings = mapSettings.Value;

            }

            /// <summary>
            /// Calculates compensated distance for time-based fare compensation
            /// If average speed < 66 km/h, compensate drivers by calculating fare based on 60 km/h standard
            /// </summary>
            /// <param name="actualDistanceKM">Actual distance from API in kilometers</param>
            /// <param name="journeyTimeSeconds">Journey time from API in seconds</param>
            /// <returns>Distance to use for billing (actual or compensated, whichever is higher)</returns>
            private int CalculateCompensatedDistance(int actualDistanceKM, int journeyTimeSeconds)
            {
                // Add 5-minute buffer to journey time as per business requirement
                int bufferedTimeSeconds = journeyTimeSeconds + (5 * 60); // Add 5 minutes
                double bufferedTimeHours = bufferedTimeSeconds / 3600.0;

                // Calculate average speed: Distance / Time
                double averageSpeed = actualDistanceKM / bufferedTimeHours;

                Console.WriteLine($"Journey Analysis:");
                Console.WriteLine($"  Actual Time: {journeyTimeSeconds / 3600.0:F2} hours");
                Console.WriteLine($"  Buffered Time: {bufferedTimeHours:F2} hours (+5 min buffer)");
                Console.WriteLine($"  Average Speed: {averageSpeed:F1} km/h");

                // Time-based compensation logic:
                // If average speed < 66 km/h, calculate compensated distance using 60 km/h standard
                if (averageSpeed < 66)
                {
                    // Compensated distance = 60 km/h × buffered journey time
                    int compensatedDistance = (int)Math.Round(60 * bufferedTimeHours);

                    // Use the higher value between actual and compensated distance
                    int billingDistance = Math.Max(actualDistanceKM, compensatedDistance);

                    Console.WriteLine($"  Speed < 66 km/h: Applying time compensation");
                    Console.WriteLine($"  Compensated Distance: {compensatedDistance} KM (60 km/h × {bufferedTimeHours:F2} hrs)");
                    Console.WriteLine($"  Billing Distance: {billingDistance} KM (max of actual vs compensated)");

                    return billingDistance;
                }
                else
                {
                    Console.WriteLine($"  Speed >= 66 km/h: Using actual distance (no compensation needed)");
                    return actualDistanceKM;
                }
            }

            private CarCategory GetCarCategory(int carCategoryID)
            {
                CarCategory category;
                var perKmChargesSQL = "SELECT PKID,Car_Category_Abbr as CategoryName,Features,Capacity,Car_Categroy_Image as CategoryImage ,Per_KM_fare as PerKMCharges, Base_Fare as BaseFare FROM RLT_CAR_CATEGORY WHERE Is_Active =1 and PKID=" + carCategoryID + "";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var response = connection.QuerySingleOrDefault<CarCategory>(perKmChargesSQL);

                    category = new CarCategory()
                    {
                        PKID = response.PKID,
                        CategoryName = response.CategoryName,
                        CategoryImage = response.CategoryImage,
                        PerKMCharges = response.PerKMCharges,
                        BaseFare = response.BaseFare,
                        Capacity = response.Capacity,
                        Features = response.Features
                    };

                    connection.Close();
                }

                return category;
            }

            public async Task<Response<List<FareDistanceCalculateResponse>>> Handle(GetFareByParamQuery query, CancellationToken cancellationToken)
            {
                Console.WriteLine("=== GetFareByParamQuery Handler Started ===");
                Console.WriteLine($"PickUp: {query.PickUpAddressLongLat}");
                Console.WriteLine($"DropOff: {query.DropOffAddressLongLat}");
                Console.WriteLine($"TripType: {query.TripType}");

                string perKmCharges = string.Empty;
                string bookingRuleFare = string.Empty;
                string carCategoryId = string.Empty;
                int tripTypeId=0 ;

                IList<CarCategory> carCategoriesList = new List<CarCategory>();

                List<BookingFareRules> bookingFareRulesList = new List<BookingFareRules>();
                List<FareDistanceCalculateResponse> responsesList = new List<FareDistanceCalculateResponse>();

                int Per_KM_fare = 0;
                decimal Basic_Fare = 0, GST_Fare;
                string fixRateNote = "";

                string address = query.PickUpAddressLongLat + ";" + query.DropOffAddressLongLat;
                Console.WriteLine($"Combined Address for API: {address}");

                HttpWebRequest req = null;
                HttpWebResponse res = null;
                // string url = _mapSettings.DistnaceMatrixAPI + _mapSettings.RestAPIKey + " /distance_matrix/driving/" + address + "";
                string url = "https://apis.mapmyindia.com/advancedmaps/v1/" + _mapSettings.RestAPIKey + "/distance_matrix/driving/" + address + "";
                Console.WriteLine($"Distance Matrix API URL: {url}");

                req = (HttpWebRequest)WebRequest.Create(url);
                req.Method = "GET";
                req.ContentType = "application/json; charset=utf-8";
                ASCIIEncoding encoder = new ASCIIEncoding();
                res = (HttpWebResponse)req.GetResponse();
                Stream responseStream = res.GetResponseStream();
                var streamReader = new StreamReader(responseStream);
                string responseString = streamReader.ReadToEnd();
                Console.WriteLine($"API Response: {responseString}");

                int distances = 0;
                int actualDistanceKM = 0;
                int journeyTimeSeconds = 0;
                string duration = "";
                if (responseString != "")
                {
                    var jsonObj = JObject.Parse(responseString);
                    JArray distancesArr = (JArray)jsonObj.SelectToken("results.distances[0]");
                    actualDistanceKM = Convert.ToInt32(distancesArr[1]) / 1000;
                    JArray durationsArr = (JArray)jsonObj.SelectToken("results.durations[0]");
                    journeyTimeSeconds = Convert.ToInt32(durationsArr[1]);
                    int hours = journeyTimeSeconds / 3600;
                    int mins = (journeyTimeSeconds % 3600) / 60;
                    duration = hours + " hrs " + mins + " mins ";
                }

                // Apply time-based fare compensation logic
                // If average speed < 50 km/h, calculate fare based on time compensation
                distances = CalculateCompensatedDistance(actualDistanceKM, journeyTimeSeconds);

                Console.WriteLine($"Actual Distance: {actualDistanceKM} KM");
                Console.WriteLine($"Journey Time: {duration}");
                Console.WriteLine($"Billing Distance (after compensation): {distances} KM");

                var tripTypeSQL = "SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type ='"+query.TripType+"'";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                  var tripType = await connection.QuerySingleOrDefaultAsync<int>(tripTypeSQL);
                  tripTypeId = tripType;
                    connection.Close();
                }

                Console.WriteLine($"Trip Type ID: {tripTypeId}");

                var perKmChargesSQL = "SELECT PKID,Car_Category_Abbr as CategoryName,Features,Capacity,Car_Categroy_Image as CategoryImage ,Per_KM_fare as PerKMCharges, Base_Fare as BaseFare FROM RLT_CAR_CATEGORY WHERE Is_Active =1";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var response = await connection.QueryAsync<CarCategory>(perKmChargesSQL);
                    Console.WriteLine($"Found {response.Count()} car categories");

                    foreach (var item in response)
                    {
                        CarCategory carCategory = new CarCategory()
                        {
                            PKID = item.PKID,
                            CategoryName = item.CategoryName,
                            CategoryImage = item.CategoryImage,

                            PerKMCharges = item.PerKMCharges,
                            BaseFare = item.BaseFare,
                            Capacity = item.Capacity,
                            Features = item.Features
                        };
                        carCategoriesList.Add(carCategory);
                        Console.WriteLine($"Car Category: {item.CategoryName}, Per KM: {item.PerKMCharges}, Base Fare: {item.BaseFare}");
                    }

                    connection.Close();
                }

  
                string carCategoryID = string.Empty;
                var carCategoryIDSQL = "SELECT PKID FROM RLT_CAR_CATEGORY WHERE Is_Active =1";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var response = await connection.QueryAsync<string>(perKmChargesSQL);
                    foreach (var item in response)
                        carCategoryID = carCategoryID + item + ",";
                    carCategoryID = carCategoryID.Remove(carCategoryID.Length - 1);

                    connection.Close();
                }

                var bookingRuleSQL = "SELECT PKID,Car_Category_Id as CarCategoryId,Trip_Type_Id as TripType, NewDistance as FixedFare FROM RLT_BOOKING_RULES WHERE Trip_Type_Id=" + tripTypeId+ " and Car_Category_Id  in ("+ carCategoryID + ") and Distance_From <= "+distances+" and Distance_To >= "+distances+"";
                Console.WriteLine($"Booking Rules SQL: {bookingRuleSQL}");

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                     connection.Open();
                    var RulesFare = await connection.QueryAsync<BookingFareRules>(bookingRuleSQL);
                    Console.WriteLine($"Found {RulesFare.Count()} booking rules");

                    foreach (var item in RulesFare)
                    {
                        BookingFareRules bookingFareRules = new BookingFareRules()
                        {
                            PKID = item.PKID,
                            FixedFare = item.FixedFare,
                            TripType= item.TripType,
                            CarCategoryId= item.CarCategoryId,
                        };

                        bookingFareRulesList.Add(bookingFareRules);
                        Console.WriteLine($"Booking Rule: Category {item.CarCategoryId}, Fixed Fare: {item.FixedFare}");
                    }

                    connection.Close();

                }


                if (bookingFareRulesList.Count>0)
                {
                    Console.WriteLine("=== Using Fixed Fare Rules ===");

                    foreach (var item in bookingFareRulesList)
                    {
                        decimal fixedFare = item.FixedFare;
                        decimal gstAmount = (fixedFare * 5) / 100;
                        decimal finalFare = fixedFare + gstAmount;

                        Console.WriteLine($"Fixed Fare Calculation - Category: {item.CarCategoryId}, Fixed: {fixedFare}, GST: {gstAmount}, Final: {finalFare}");

                        FareDistanceCalculate fareDistanceCalculate = new FareDistanceCalculate()
                        {
                            Distance = distances.ToString("#.##"),
                            BasicFare = Math.Round(fixedFare, 0).ToString(),
                            GSTFare = Math.Round(gstAmount, 0).ToString(),
                            Fare = Math.Round(finalFare, 0).ToString(),
                            Duration = duration,
                            FixRateNote = "Fixed fare rule applied: ₹" + Math.Round(finalFare, 0).ToString(),
                        };

                        FareDistanceCalculateResponse fareDistanceResponse = new FareDistanceCalculateResponse()
                        {
                            FareDistanceCalculateList = fareDistanceCalculate,
                            CarCategoryList = GetCarCategory(item.CarCategoryId)
                        };

                        responsesList.Add(fareDistanceResponse);
                    }
                }
                else
                {
                    Console.WriteLine("=== Using Per-KM Pricing with Base Fare ===");

                    foreach (var item in carCategoriesList)
                    {
                        // NEW PRICING LOGIC: MAX(Base Fare, Distance Fare) + GST
                        decimal baseFare = item.BaseFare;
                        decimal originalPerKmRate = Convert.ToDecimal(item.PerKMCharges);

                        // Apply distance-based rate adjustment: +1 per KM if distance < 200 KM
                        decimal adjustedPerKmRate = originalPerKmRate;
                        if (distances < 200)
                        {
                            adjustedPerKmRate = originalPerKmRate + 1;
                            Console.WriteLine($"Distance < 200 KM: Adjusted per KM rate from ₹{originalPerKmRate} to ₹{adjustedPerKmRate}");
                        }
                        else
                        {
                            Console.WriteLine($"Distance >= 200 KM: Using original per KM rate ₹{originalPerKmRate}");
                        }

                        // Calculate distance fare
                        decimal distanceFare = adjustedPerKmRate * distances;

                        // NEW LOGIC: Take maximum of base fare or distance fare
                        decimal basicFare = Math.Max(baseFare, distanceFare);
                        decimal gstAmount = (basicFare * 5) / 100;
                        decimal finalFare = basicFare + gstAmount;

                        Console.WriteLine($"Fare Calculation for {item.CategoryName}:");
                        Console.WriteLine($"  Base Fare: ₹{baseFare}");
                        Console.WriteLine($"  Billing Distance: {distances} KM");
                        Console.WriteLine($"  Per KM Rate: ₹{adjustedPerKmRate} (Original: ₹{originalPerKmRate})");
                        Console.WriteLine($"  Distance Fare: ₹{distanceFare.ToString("#.##")} ({distances} KM × ₹{adjustedPerKmRate})");
                        Console.WriteLine($"  Basic Fare: ₹{basicFare.ToString("#.##")} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare.ToString("#.##")})]");
                        Console.WriteLine($"  GST (5%): ₹{gstAmount.ToString("#.##")}");
                        Console.WriteLine($"  Final Fare: ₹{finalFare.ToString("#.##")}");

                        // Build comprehensive rate note with new pricing logic
                        string fareType = basicFare == baseFare ? "Base Fare" : "Distance Fare";
                        string rateNote = $"{fareType}: ₹{basicFare.ToString("#.##")} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare.ToString("#.##")})]";

                        // Add distance calculation details
                        rateNote += $" (Distance: {distances} KM × ₹{adjustedPerKmRate}/KM)";

                        // Add distance-based rate adjustment note
                        if (distances < 200)
                        {
                            rateNote += " [+₹1/KM for trips < 200 KM]";
                        }

                        // Add time compensation note if billing distance differs from actual distance
                        if (distances != actualDistanceKM)
                        {
                            rateNote += $" [Time compensation: billing {distances} KM vs actual {actualDistanceKM} KM]";
                        }

                        FareDistanceCalculate fareDistanceCalculate = new FareDistanceCalculate()
                        {
                            Distance = distances.ToString("#.##"),
                            BasicFare = Math.Round(basicFare, 0).ToString(),
                            GSTFare = Math.Round(gstAmount, 0).ToString(),
                            Fare = Math.Round(finalFare, 0).ToString(),
                            Duration = duration,
                            FixRateNote = rateNote,
                        };

                        FareDistanceCalculateResponse fareDistanceResponse = new FareDistanceCalculateResponse()
                        {
                            FareDistanceCalculateList = fareDistanceCalculate,
                            CarCategoryList = GetCarCategory(item.PKID)
                        };

                        responsesList.Add(fareDistanceResponse);
                    }
                }

                Console.WriteLine($"=== Final Results: {responsesList.Count} fare options generated ===");
                foreach (var response in responsesList)
                {
                    Console.WriteLine($"Category: {response.CarCategoryList.CategoryName}, Final Fare: ₹{response.FareDistanceCalculateList.Fare}");
                }
                Console.WriteLine("=== GetFareByParamQuery Handler Completed ===");

                return new Response<List<FareDistanceCalculateResponse>>(responsesList);
            }
        
          
        }
    }
}
