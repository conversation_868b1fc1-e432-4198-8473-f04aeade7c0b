using Application.DTOs.Payment;
using Application.Interfaces.Repositories;
using Application.Interfaces.Services;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;
using System.Net.Http;
using System.Text;

namespace Application.Features.Payments.PhonePe
{
    public class VerifyPhonePePaymentCommand : IRequest<Response<PhonePeVerifyData>>
    {
        public PhonePeVerifyRequest VerifyRequest { get; set; }
    }

    public class VerifyPhonePePaymentCommandHandler : IRequestHandler<VerifyPhonePePaymentCommand, Response<PhonePeVerifyData>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IConfiguration _configuration;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IPhonePeAuthService _phonePeAuthService;

        public VerifyPhonePePaymentCommandHandler(
            IBookingRepositoryAsync bookingRepository,
            IConfiguration configuration,
            IHttpClientFactory httpClientFactory,
            IPhonePeAuthService phonePeAuthService)
        {
            _bookingRepository = bookingRepository;
            _configuration = configuration;
            _httpClientFactory = httpClientFactory;
            _phonePeAuthService = phonePeAuthService;
        }

        public async Task<Response<PhonePeVerifyData>> Handle(VerifyPhonePePaymentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine($"[VerifyPhonePePayment] Starting payment verification for MerchantTransactionId: {request.VerifyRequest.MerchantTransactionId}, OrderId: {request.VerifyRequest.OrderId}");

                // Get booking details
                var booking = await _bookingRepository.GetByUniqueIdAsync(request.VerifyRequest.MerchantTransactionId);
                Console.WriteLine($"[VerifyPhonePePayment] Booking lookup result - Found: {(booking != null)}, BookingId: {(booking?.BookingID ?? "N/A")}");

                if (booking == null)
                {
                    Console.WriteLine($"[VerifyPhonePePayment] Error: Booking not found for MerchantTransactionId: {request.VerifyRequest.MerchantTransactionId}");
                    return new Response<PhonePeVerifyData>
                    {
                        Succeeded = false,
                        Message = "Booking not found"
                    };
                }

                // Get access token
                Console.WriteLine("[VerifyPhonePePayment] Requesting PhonePe access token");
                var accessToken = await _phonePeAuthService.GetAccessTokenAsync();
                Console.WriteLine($"[VerifyPhonePePayment] Access token received: {(string.IsNullOrEmpty(accessToken) ? "Failed" : "Success")}");

                // Get PhonePe settings
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");
                var apiUrl = phonePeSettings["ApiUrl"];
                var merchantId = phonePeSettings["MerchantId"];
                Console.WriteLine($"[VerifyPhonePePayment] Using PhonePe API URL: {apiUrl}, MerchantId: {merchantId}");

                // Call order status API
                var client = _httpClientFactory.CreateClient("PhonePe");
                client.DefaultRequestHeaders.Add("Authorization", $"O-Bearer {accessToken}");

                var statusUrl = $"{apiUrl}/checkout/v2/order/{request.VerifyRequest.MerchantTransactionId}/status";
                Console.WriteLine($"[VerifyPhonePePayment] Calling PhonePe status API: {statusUrl}");

                var response = await client.GetAsync(statusUrl);
                Console.WriteLine($"[VerifyPhonePePayment] PhonePe API Response Status: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[VerifyPhonePePayment] Error response from PhonePe API: {errorContent}");
                    return new Response<PhonePeVerifyData>
                    {
                        Succeeded = false,
                        Message = "Failed to verify payment status"
                    };
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[VerifyPhonePePayment] PhonePe API Response Content: {responseContent}");

                var phonePeResponse = JsonSerializer.Deserialize<PhonePeOrderStatusResponse>(responseContent);

                // Update booking payment status
                var phonePeStatus = phonePeResponse.state;
                var transactionId = "";
                var paymentId = "";

                Console.WriteLine($"[VerifyPhonePePayment] Payment Status from PhonePe: {phonePeStatus}");

                // Map PhonePe status to application payment status
                string paymentStatus;
                switch (phonePeStatus?.ToUpper())
                {
                    case "SUCCESS":
                        paymentStatus = "Success";
                        break;
                    case "FAILED":
                        paymentStatus = "Failed";
                        break;
                    case "PENDING":
                        paymentStatus = "Pending";
                        break;
                    default:
                        paymentStatus = "Pending";
                        break;
                }

                Console.WriteLine($"[VerifyPhonePePayment] Mapped Payment Status: {paymentStatus}");

                // Extract transaction details if payment is completed
                if (paymentStatus == "COMPLETED" && phonePeResponse.paymentDetails != null && phonePeResponse.paymentDetails.Count > 0)
                {
                    var latestPayment = phonePeResponse.paymentDetails[0];
                    transactionId = latestPayment.transactionId;
                    paymentId = latestPayment.transactionId;
                    Console.WriteLine($"[VerifyPhonePePayment] Payment completed - TransactionId: {transactionId}, PaymentId: {paymentId}");
                }

                // Update booking with payment details
                Console.WriteLine($"[VerifyPhonePePayment] Updating booking with payment details - Status: {paymentStatus}, PaymentId: {paymentId}, OrderId: {request.VerifyRequest.OrderId}");
                booking.RazorpayStatus = paymentStatus;
                booking.RazorpayPaymentId = paymentId;
                booking.RazorpayOrderid = request.VerifyRequest.OrderId;
                await _bookingRepository.UpdateAsync(booking);
                Console.WriteLine("[VerifyPhonePePayment] Booking updated successfully");

                var verifyResponse = new PhonePeVerifyData
                {
                    PaymentStatus = paymentStatus,
                    TransactionId = transactionId,
                    OrderId = request.VerifyRequest.OrderId,
                    BookingId = booking.BookingID,
                    Amount = booking.Fare ?? 0,
                    PaymentId = paymentId,
                    PaymentType = booking.PaymentType ?? "FULL",
                    PartialPaymentAmount = booking.PartialPaymentAmount,
                    RemainingAmountForDriver = booking.RemainingAmountForDriver
                };

                Console.WriteLine($"[VerifyPhonePePayment] Verification completed successfully for BookingId: {booking.BookingID}");
                return new Response<PhonePeVerifyData>(verifyResponse, "Payment verification completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[VerifyPhonePePayment] Exception occurred: {ex.Message}");
                Console.WriteLine($"[VerifyPhonePePayment] Stack Trace: {ex.StackTrace}");
                return new Response<PhonePeVerifyData>
                {
                    Succeeded = false,
                    Message = $"Error verifying payment: {ex.Message}"
                };
            }
        }
    }
}