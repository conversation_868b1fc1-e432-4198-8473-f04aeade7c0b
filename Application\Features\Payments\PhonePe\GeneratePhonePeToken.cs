using Application.DTOs.Payment;
using Application.Interfaces.Repositories;
using Application.Interfaces.Services;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;
using System.Net.Http;
using System.Text;
using Application.Features.Bookings.CreateBooking;
using AutoMapper;
using Domain.Entities;

namespace Application.Features.Payments.PhonePe
{
    public class GeneratePhonePeTokenCommand : IRequest<Response<PhonePePaymentResponse>>
    {
        public BookingCommand BookingCommand { get; set; }
    }

    public class GeneratePhonePeTokenCommandHandler : IRequestHandler<GeneratePhonePeTokenCommand, Response<PhonePePaymentResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly IPhonePeAuthService _phonePeAuthService;
        private readonly IHttpClientFactory _httpClientFactory;
        private const long MINIMUM_AMOUNT_PAISE = 100; // 1 rupee minimum

        public GeneratePhonePeTokenCommandHandler(
            IBookingRepositoryAsync bookingRepository,
            IConfiguration configuration,
            IMapper mapper,
            IPhonePeAuthService phonePeAuthService,
            IHttpClientFactory httpClientFactory)
        {
            _bookingRepository = bookingRepository;
            _configuration = configuration;
            _mapper = mapper;
            _phonePeAuthService = phonePeAuthService;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<Response<PhonePePaymentResponse>> Handle(GeneratePhonePeTokenCommand request, CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine("Starting payment token generation for booking command");
                Console.WriteLine($"Original Fare amount: {request.BookingCommand.Fare}");
                Console.WriteLine($"Payment Type: {request.BookingCommand.PaymentType}");
                Console.WriteLine($"Partial Payment Amount: {request.BookingCommand.PartialPaymentAmount}");

                // Validate fare amount
                if (!request.BookingCommand.Fare.HasValue || request.BookingCommand.Fare.Value <= 0)
                {
                    Console.WriteLine($"Invalid fare amount: {request.BookingCommand.Fare}");
                    return new Response<PhonePePaymentResponse>
                    {
                        Succeeded = false,
                        Message = "Invalid fare amount"
                    };
                }

                // Determine payment amount based on payment type
                decimal paymentAmount;
                if (request.BookingCommand.PaymentType == "PARTIAL" && request.BookingCommand.PartialPaymentAmount.HasValue)
                {
                    paymentAmount = request.BookingCommand.PartialPaymentAmount.Value;
                    Console.WriteLine($"Using partial payment amount: {paymentAmount}");

                    // Calculate remaining amount for driver
                    request.BookingCommand.RemainingAmountForDriver = request.BookingCommand.Fare.Value - paymentAmount;
                    Console.WriteLine($"Remaining amount for driver: {request.BookingCommand.RemainingAmountForDriver}");
                }
                else
                {
                    // Full payment
                    paymentAmount = request.BookingCommand.Fare.Value;
                    request.BookingCommand.PaymentType = "FULL";
                    request.BookingCommand.RemainingAmountForDriver = 0;
                    Console.WriteLine($"Using full payment amount: {paymentAmount}");
                }

                // Calculate amount in paise
                var amountInPaise = Convert.ToInt64(paymentAmount * 100);
                Console.WriteLine($"Amount in paise: {amountInPaise}");

                // Validate minimum amount
                if (amountInPaise < MINIMUM_AMOUNT_PAISE)
                {
                    Console.WriteLine($"Amount {amountInPaise} paise is less than minimum allowed amount {MINIMUM_AMOUNT_PAISE} paise");
                    return new Response<PhonePePaymentResponse>
                    {
                        Succeeded = false,
                        Message = $"Order amount {paymentAmount} is less than minimum amount allowed (₹1)"
                    };
                }

                // Map BookingCommand to RLT_BOOKING entity
                var bookingEntity = _mapper.Map<RLT_BOOKING>(request.BookingCommand);
                Console.WriteLine($"Mapped booking entity with ID: {bookingEntity.BookingID}");

                // Generate booking ID and create booking
                var bookingResponse = await _bookingRepository.AddNewAsync(bookingEntity);
                if (bookingResponse == null)
                {
                    Console.WriteLine("Failed to create booking");
                    return new Response<PhonePePaymentResponse>
                    {
                        Succeeded = false,
                        Message = "Failed to create booking"
                    };
                }

                Console.WriteLine($"Booking created successfully with ID: {bookingResponse.BookingId}");

                // Get PhonePe settings
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");
                var apiUrl = phonePeSettings["ApiUrl"];
                var redirectUrl = phonePeSettings["RedirectUrl"];

                // Get access token
                var accessToken = await _phonePeAuthService.GetAccessTokenAsync();
                Console.WriteLine("Retrieved PhonePe access token");

                // Prepare payment request
                var paymentRequest = new PhonePePaymentInitiateRequest
                {
                    MerchantOrderId = bookingResponse.BookingId,
                    Amount = amountInPaise,
                    PaymentFlow = new PhonePePaymentFlow
                    {
                        Type = "PG_CHECKOUT",
                        Message = $"Payment for booking {bookingResponse.BookingId}",
                        MerchantUrls = new PhonePeMerchantUrls
                        {
                            RedirectUrl = redirectUrl
                        }
                    },
                    MetaInfo = new PhonePeMetaInfo
                    {
                        Udf1 = request.BookingCommand.TravelerName,
                        Udf2 = request.BookingCommand.PhoneNumber,
                        Udf3 = request.BookingCommand.PickUpAddress,
                        Udf4 = request.BookingCommand.DropOffAddress,
                        Udf5 = request.BookingCommand.CarCategory
                    }
                };

                Console.WriteLine($"Prepared payment request for amount: {paymentRequest.Amount} paise");
                Console.WriteLine($"Payment request details: {JsonSerializer.Serialize(paymentRequest)}");

                // Make API call to initiate payment
                var client = _httpClientFactory.CreateClient("PhonePe");
                client.DefaultRequestHeaders.Add("Authorization", $"O-Bearer {accessToken}");

                var content = new StringContent(
                    JsonSerializer.Serialize(paymentRequest),
                    Encoding.UTF8,
                    "application/json");

                Console.WriteLine("Sending payment initiation request to PhonePe");
                var response = await client.PostAsync($"{apiUrl}/checkout/v2/pay", content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"PhonePe API error: {response.StatusCode} - {errorContent}");
                    return new Response<PhonePePaymentResponse>
                    {
                        Succeeded = false,
                        Message = $"PhonePe API error: {response.StatusCode} - {errorContent}"
                    };
                }

                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Received response from PhonePe: {responseContent}");
                
                var phonePeResponse = JsonSerializer.Deserialize<PhonePePaymentInitiateResponse>(responseContent);

                var paymentResponse = new PhonePePaymentResponse
                {
                    TokenUrl = phonePeResponse.RedirectUrl,
                    MerchantTransactionId = bookingResponse.BookingId,
                    OrderId = phonePeResponse.OrderId
                };

                Console.WriteLine($"Successfully generated payment URL for booking {bookingResponse.BookingId}");
                return new Response<PhonePePaymentResponse>(paymentResponse);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating payment URL: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return new Response<PhonePePaymentResponse>
                {
                    Succeeded = false,
                    Message = $"Error generating payment URL: {ex.Message}"
                };
            }
        }
    }
} 