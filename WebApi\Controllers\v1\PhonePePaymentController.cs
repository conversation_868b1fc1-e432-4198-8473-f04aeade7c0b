using Application.DTOs.Payment;
using Application.Features.Bookings.CreateBooking;
using Application.Features.Payments.PhonePe;
using Application.Wrappers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Text.Json;
using System;
using System.Linq;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/payments/phonepe")]
    public class PhonePePaymentController : BaseApiController
    {
        [HttpPost("token")]
        [Authorize]
        public async Task<IActionResult> GeneratePaymentToken([FromBody] BookingCommand command)
        {
            var generateTokenCommand = new GeneratePhonePeTokenCommand
            {
                BookingCommand = command
            };

            var result = await Mediator.Send(generateTokenCommand);
            return Ok(result);
        }

        [HttpPost("verify")]
        public async Task<IActionResult> VerifyPayment([FromBody] PhonePeVerifyRequest request)
        {
            Console.WriteLine($"[PhonePePaymentController] VerifyPayment Request Payload:");
            Console.WriteLine($"[PhonePePaymentController] PhonePeVerifyRequest: {JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Query Parameters: {JsonSerializer.Serialize(Request.Query.ToDictionary(q => q.Key, q => q.Value.ToString()), new JsonSerializerOptions { WriteIndented = true })}");

            var verifyCommand = new VerifyPhonePePaymentCommand
            {
                VerifyRequest = request
            };

            var result = await Mediator.Send(verifyCommand);
            return Ok(result);
        }

        [HttpPost("callback")]
        public async Task<IActionResult> PaymentCallback([FromBody] PhonePeVerifyRequest request)
        {
            Console.WriteLine($"[PhonePePaymentController] PaymentCallback Request Payload:");
            Console.WriteLine($"[PhonePePaymentController] PhonePeVerifyRequest: {JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Query Parameters: {JsonSerializer.Serialize(Request.Query, new JsonSerializerOptions { WriteIndented = true })}");
            Console.WriteLine($"[PhonePePaymentController] Headers: {JsonSerializer.Serialize(Request.Headers, new JsonSerializerOptions { WriteIndented = true })}");

            // This endpoint will be called by PhonePe after payment completion
            var verifyCommand = new VerifyPhonePePaymentCommand
            {
                VerifyRequest = request
            };

            var result = await Mediator.Send(verifyCommand);
            return Ok(result);
        }
    }
} 